import React, { createContext, useContext, useState, useEffect } from 'react';
import { authAPI } from '../lib/apiClient';
import { handleError } from '../lib/utils';
import toast from 'react-hot-toast';

const AuthContext = createContext(null);

export const AuthProvider = ({ children }) => {
    const [user, setUser] = useState(null);
    const [isAuthenticated, setIsAuthenticated] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        const initializeAuth = () => {
            try {
                const userData = sessionStorage.getItem('user');
                const token = sessionStorage.getItem('token');

                if (userData && token) {
                    const parsedUser = JSON.parse(userData);
                    setUser(parsedUser);
                    setIsAuthenticated(true);
                }
            } catch (error) {
                console.error('Auth initialization error:', error);
                sessionStorage.clear();
            } finally {
                setIsLoading(false);
            }
        };

        initializeAuth();
    }, []);

    const login = async (credentials) => {
        try {
            setIsLoading(true);
            setError(null);

            const response = await authAPI.login(credentials);
            const { user, access_token } = response.data;

            sessionStorage.setItem('token', access_token);
            sessionStorage.setItem('user', JSON.stringify(user));

            setUser(user);
            setIsAuthenticated(true);
            toast.success(`Welcome back, ${user.name}!`);

            return { success: true, user };
        } catch (error) {
            const errorInfo = handleError(error, 'login');
            setError(errorInfo.message);
            toast.error(errorInfo.message);
            return { success: false, error: errorInfo.message };
        } finally {
            setIsLoading(false);
        }
    };

    const register = async (userData) => {
        try {
            setIsLoading(true);
            setError(null);

            const response = await authAPI.register(userData);
            const { user, access_token } = response.data;

            sessionStorage.setItem('token', access_token);
            sessionStorage.setItem('user', JSON.stringify(user));

            setUser(user);
            setIsAuthenticated(true);
            toast.success(`Welcome, ${user.name}! Your account has been created.`);

            return { success: true, user };
        } catch (error) {
            const errorInfo = handleError(error, 'register');
            setError(errorInfo.message);
            toast.error(errorInfo.message);
            return { success: false, error: errorInfo.message };
        } finally {
            setIsLoading(false);
        }
    };

    const logout = async () => {
        try {
            setIsLoading(true);

            try {
                await authAPI.logout();
            } catch (error) {
                console.warn('Logout API call failed:', error);
            }

            sessionStorage.clear();
            setUser(null);
            setIsAuthenticated(false);
            setError(null);
            toast.success('You have been logged out successfully.');

            return { success: true };
        } catch (error) {
            const errorInfo = handleError(error, 'logout');
            sessionStorage.clear();
            setUser(null);
            setIsAuthenticated(false);
            setError(null);
            return { success: false, error: errorInfo.message };
        } finally {
            setIsLoading(false);
        }
    };

    const forgotPassword = async (email) => {
        try {
            setIsLoading(true);
            setError(null);

            await authAPI.forgotPassword(email);
            toast.success('Password reset instructions have been sent to your email.');

            return { success: true };
        } catch (error) {
            const errorInfo = handleError(error, 'forgot password');
            setError(errorInfo.message);
            toast.error(errorInfo.message);
            return { success: false, error: errorInfo.message };
        } finally {
            setIsLoading(false);
        }
    };

    const resetPassword = async (token, password, passwordConfirmation) => {
        try {
            setIsLoading(true);
            setError(null);

            await authAPI.resetPassword(token, password, passwordConfirmation);
            toast.success('Your password has been reset successfully. Please login with your new password.');

            return { success: true };
        } catch (error) {
            const errorInfo = handleError(error, 'reset password');
            setError(errorInfo.message);
            toast.error(errorInfo.message);
            return { success: false, error: errorInfo.message };
        } finally {
            setIsLoading(false);
        }
    };

    const updateProfile = async (userData) => {
        try {
            setIsLoading(true);
            setError(null);

            const response = await authAPI.updateProfile(userData);
            const updatedUser = response.data.user;

            sessionStorage.setItem('user', JSON.stringify(updatedUser));
            setUser(updatedUser);
            toast.success('Profile updated successfully.');

            return { success: true, user: updatedUser };
        } catch (error) {
            const errorInfo = handleError(error, 'update profile');
            setError(errorInfo.message);
            toast.error(errorInfo.message);
            return { success: false, error: errorInfo.message };
        } finally {
            setIsLoading(false);
        }
    };

    const clearError = () => {
        setError(null);
    };

    const value = {
        user,
        isAuthenticated,
        isLoading,
        error,
        login,
        register,
        logout,
        forgotPassword,
        resetPassword,
        updateProfile,
        clearError,
    };

    return (
        <AuthContext.Provider value={value}>
            {children}
        </AuthContext.Provider>
    );
};

export const useAuth = () => {
    const context = useContext(AuthContext);

    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }

    return context;
};

export default AuthContext;
